"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";

export default function FeaturesSection() {
  return (
    <section className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Feature Cards Grid - Givingly Style */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Project Management Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-500 to-purple-600 p-8 text-white"
          >
            <div className="relative z-10">
              <h3 className="mb-4 text-2xl font-semibold">
                Project Management
              </h3>
              <p className="mb-6 text-purple-100">
                Organize your projects with unlimited issues, teams, and file
                uploads. Track progress with ease.
              </p>
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-white/20">
                  📊
                </div>
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-white/20">
                  📋
                </div>
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-white/20">
                  📈
                </div>
              </div>
            </div>
            <div className="absolute -top-4 -right-4 h-32 w-32 rounded-full bg-white/10" />
            <div className="absolute -bottom-8 -left-8 h-24 w-24 rounded-full bg-white/5" />
          </motion.div>

          {/* Team Collaboration Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
            className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-pink-500 to-rose-500 p-8 text-white"
          >
            <div className="relative z-10">
              <h3 className="mb-4 text-2xl font-semibold">
                Team Collaboration
              </h3>
              <p className="mb-6 text-pink-100">
                Work together seamlessly with unlimited members, admin roles,
                and private teams.
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="h-6 w-6 rounded-full bg-white/30" />
                  <div className="h-2 w-20 rounded bg-white/20" />
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-6 w-6 rounded-full bg-white/30" />
                  <div className="h-2 w-16 rounded bg-white/20" />
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-6 w-6 rounded-full bg-white/30" />
                  <div className="h-2 w-24 rounded bg-white/20" />
                </div>
              </div>
            </div>
            <div className="absolute -top-6 -right-6 h-28 w-28 rounded-full bg-white/10" />
          </motion.div>

          {/* AI-Powered Insights Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
            className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-emerald-500 to-teal-500 p-8 text-white"
          >
            <div className="relative z-10">
              <h3 className="mb-4 text-2xl font-semibold">AI Insights</h3>
              <p className="mb-6 text-emerald-100">
                Get intelligent insights and ask AI for help with your projects
                and workflows.
              </p>
              <div className="rounded-lg bg-white/20 p-3">
                <div className="mb-2 flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-emerald-300" />
                  <div className="h-1 w-16 rounded bg-white/40" />
                </div>
                <div className="mb-2 flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-emerald-300" />
                  <div className="h-1 w-20 rounded bg-white/40" />
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-emerald-300" />
                  <div className="h-1 w-12 rounded bg-white/40" />
                </div>
              </div>
            </div>
            <div className="absolute -right-4 -bottom-4 h-20 w-20 rounded-full bg-white/10" />
          </motion.div>

          {/* Security & Authentication Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.5 }}
            className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-500 to-indigo-600 p-8 text-white"
          >
            <div className="relative z-10">
              <h3 className="mb-4 text-2xl font-semibold">Security</h3>
              <p className="mb-6 text-blue-100">
                Enterprise-grade security with Google SSO, SAML, and advanced
                security features.
              </p>
              <div className="grid grid-cols-2 gap-2">
                <div className="rounded-lg bg-white/20 p-2 text-center text-xs">
                  SSO
                </div>
                <div className="rounded-lg bg-white/20 p-2 text-center text-xs">
                  SAML
                </div>
                <div className="rounded-lg bg-white/20 p-2 text-center text-xs">
                  2FA
                </div>
                <div className="rounded-lg bg-white/20 p-2 text-center text-xs">
                  SCIM
                </div>
              </div>
            </div>
            <div className="absolute -top-4 -left-4 h-24 w-24 rounded-full bg-white/10" />
          </motion.div>

          {/* Analytics & Tracking Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.6 }}
            className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-amber-500 to-orange-500 p-8 text-white"
          >
            <div className="relative z-10">
              <h3 className="mb-4 text-2xl font-semibold">Analytics</h3>
              <p className="mb-6 text-amber-100">
                Track your habits, goals, and progress with comprehensive
                analytics and insights.
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-amber-200">Tasks</span>
                  <span className="text-xs">85%</span>
                </div>
                <div className="h-1 w-full rounded-full bg-white/20">
                  <div className="h-1 w-4/5 rounded-full bg-white/60" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-amber-200">Goals</span>
                  <span className="text-xs">92%</span>
                </div>
                <div className="h-1 w-full rounded-full bg-white/20">
                  <div className="h-1 w-11/12 rounded-full bg-white/60" />
                </div>
              </div>
            </div>
            <div className="absolute -right-6 -bottom-6 h-32 w-32 rounded-full bg-white/5" />
          </motion.div>

          {/* Mobile & Cross-Platform Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.7 }}
            className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-slate-600 to-slate-700 p-8 text-white"
          >
            <div className="relative z-10">
              <h3 className="mb-4 text-2xl font-semibold">Cross-Platform</h3>
              <p className="mb-6 text-slate-300">
                Access your workspace anywhere with our mobile app and
                cross-platform support.
              </p>
              <div className="flex items-center space-x-4">
                <div className="rounded-lg bg-white/20 p-3">
                  <div className="h-8 w-5 rounded bg-white/40" />
                </div>
                <div className="rounded-lg bg-white/20 p-3">
                  <div className="h-5 w-8 rounded bg-white/40" />
                </div>
                <div className="rounded-lg bg-white/20 p-3">
                  <div className="h-6 w-6 rounded bg-white/40" />
                </div>
              </div>
            </div>
            <div className="absolute -top-8 -right-8 h-28 w-28 rounded-full bg-white/5" />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
